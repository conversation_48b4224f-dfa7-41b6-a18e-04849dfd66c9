<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FontAwesome 图标测试</title>
    <!-- 引入 FontAwesome CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .icon-display {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 32px;
            width: 80px;
            height: 80px;
            margin: 10px;
            border: 1px solid #ddd;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .icon-info {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>FontAwesome 图标兼容性测试</h1>
    
    <div class="test-container">
        <div class="test-title">1. 传统 FontAwesome 格式 (fa fa-database)</div>
        <span class="icon-display" style="color:#1890ff;background:#e6f7ff;">
            <i class="fa fa-database"></i>
        </span>
        <div class="icon-info">类名: fa fa-database</div>
    </div>

    <div class="test-container">
        <div class="test-title">2. FontAwesome 5+ Solid 格式 (fas fa-home)</div>
        <span class="icon-display" style="color:#52c41a;background:#f6ffed;">
            <i class="fas fa-home"></i>
        </span>
        <div class="icon-info">类名: fas fa-home</div>
    </div>

    <div class="test-container">
        <div class="test-title">3. FontAwesome Regular 格式 (far fa-user)</div>
        <span class="icon-display" style="color:#722ed1;background:#f9f0ff;">
            <i class="far fa-user"></i>
        </span>
        <div class="icon-info">类名: far fa-user</div>
    </div>

    <div class="test-container">
        <div class="test-title">4. FontAwesome Brand 格式 (fab fa-github)</div>
        <span class="icon-display" style="color:#13c2c2;background:#e6fffb;">
            <i class="fab fa-github"></i>
        </span>
        <div class="icon-info">类名: fab fa-github</div>
    </div>

    <div class="test-container">
        <div class="test-title">5. SVG 图标测试</div>
        <span class="icon-display" style="color:#fa541c;background:#fff2e8;">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
        </span>
        <div class="icon-info">SVG 图标</div>
    </div>

    <script>
        // 测试正则表达式匹配
        const testCases = [
            'fa fa-database',
            'fas fa-home', 
            'far fa-user',
            'fab fa-github',
            'fa fa-star',
            '<svg>...</svg>',
            'not-fontawesome'
        ];

        console.log('FontAwesome 图标格式检测测试:');
        testCases.forEach(testCase => {
            const isFontAwesome = /^(fa[srbl]?\s+fa-|fa\s+fa-)/i.test(testCase);
            console.log(`"${testCase}" -> FontAwesome: ${isFontAwesome}`);
        });
    </script>
</body>
</html>
